import { HttpService } from '@nestjs/axios';
import { Controller, Get, Post, Body, Patch, Param, Delete , Logger} from '@nestjs/common';
import { TransactionRouterService } from './transaction-router.service';

@Controller('transaction-router')
export class TransactionRouterController {
  private readonly logger = new Logger(TransactionRouterController.name);

  constructor(private readonly transactionRouterService: TransactionRouterService) {}

  // Quicknode sends data here this is the destination_url domain/webhook/....
  // remember to chnage the webhook URL to scanner.cake
  @Post('webhooks/networks/ethereum')
  async processEthereumWebhook(@Body() payload: any) {
    this.logger.log("QuickNode is Quick with it");

    return await this.transactionRouterService.processEthereumWebhook(payload);


  }

  
}
