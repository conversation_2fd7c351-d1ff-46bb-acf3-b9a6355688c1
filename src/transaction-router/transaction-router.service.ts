import { HttpException, HttpStatus, Injectable, Logger } from '@nestjs/common';
import { firstValueFrom, isObservable, Observable } from 'rxjs';
import { AxiosResponse } from 'axios';

@Injectable()
export class TransactionRouterService {

  private readonly logger = new Logger(TransactionRouterService.name);

  protected async handleRequest<T>(
      request: Observable<AxiosResponse<T>> | Promise<AxiosResponse<T>>, // data kena error 
      operation: string
    ): Promise<T> {
      try {
        const response = isObservable(request)
          ? await firstValueFrom(request)
          : await request;
  
        this.logger.log(`YYYIPPPEEE ${operation} successful`);
        return response.data;
      } catch (error) {
        const status = error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR;
        const message = error.response?.data || `${operation} failed`;
  
        this.logger.error(`NEEIINN ${operation} failed:`, message);
        throw new HttpException(message, status);
      }
    }

  async processEthereumWebhook(payload: any) {
    this.logger.log('Processing Data',payload);
    console.log(payload);

  }


}
