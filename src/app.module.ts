import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { MoralisModule } from './moralis/moralis.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import configuration from 'config/configuration';
import { <PERSON><PERSON><PERSON> } from './moralis/entities/morali.entity';
import { Network } from './networks/entities/network.entity';
import { QueueModule } from './queue/queue.module';
import { QuicknodeModule } from './quicknode/quicknode.module';
import { TransactionRouterModule } from './transaction-router/transaction-router.module';
import { NetworksModule } from './networks/networks.module';

@Module({
  imports: [
    TypeOrmModule.forRoot({
      type: 'postgres',
      host: configuration().database.host,
      port: configuration().database.port,
      username: configuration().database.username,
      password: configuration().database.password,
      database: configuration().database.database,
      schema: configuration().database.schema,
      entities: [
        Morali,
        Network,
      ],
      synchronize: configuration().database.synchronize === 'true' ? true : false,
    }),
    MoralisModule,
    QueueModule,
    QuicknodeModule,
    TransactionRouterModule,
    NetworksModule
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
