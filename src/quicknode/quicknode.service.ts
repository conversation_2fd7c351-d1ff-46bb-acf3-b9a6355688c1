import { HttpService } from '@nestjs/axios';
import { HttpException, HttpStatus, Injectable, Logger } from '@nestjs/common';
import { firstValueFrom, isObservable, Observable } from 'rxjs';
import { AxiosResponse } from 'axios';
import { UpdateQnWebhookDto } from './dto/update-qn-webhook.dto';
import { FilterQnWebhookDto } from './dto/filter-qn-webhook.dto';

@Injectable()
export class QuicknodeService {
  private readonly logger = new Logger(QuicknodeService.name);
  private readonly baseUrl = 'https://api.quicknode.com/webhooks/rest/v1';
  private readonly apiKey = process.env.QN_API_KEY as string;

  constructor(
    private readonly HttpService: HttpService) {
    if (!this.apiKey) {
      throw new Error('QN_API_KEY is not defined');
    }
  }

  private getHeaders() {
    return {
      'x-api-key': this.apiKey,
      'Content-Type': 'application/json',
    };
  }

  // lets make a service just for this ask kraken on opinion 
  protected async handleRequest<T>(
    request: Observable<AxiosResponse<T>> | Promise<AxiosResponse<T>>, // data kena error 
    operation: string
  ): Promise<T> {
    try {
      const response = isObservable(request)
        ? await firstValueFrom(request)
        : await request;

      this.logger.log(`YYYIPPPEEE ${operation} successful`);
      return response.data;
    } catch (error) {
      const status = error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR;
      const message = error.response?.data || `${operation} failed`;

      this.logger.error(`NEEIINN ${operation} failed:`, message);
      throw new HttpException(message, status);
    }
  }

  // create webhook 
  async createWebhook(name: string, network: string, notification_email: string, webhookUrl: string) {
    const webhookData =  {
      name,
      network,
      notification_email,
      destination_attributes: {
        url: webhookUrl,
        compression: 'none'
      },
      status: 'active',
    }

    const request = this.HttpService.post(
      `${this.baseUrl}/webhooks`,
      webhookData,
      { headers: this.getHeaders() }
    );
    return this.handleRequest(request, 'Create new webhook');
  }

  async createWebhookWithTemplate(name: string , network: string, noptification_email: string, webhookUrl: string, template: string, wallets: string[]) {
    const webhookData = {
      name,
      network,
      notification_email: noptification_email,
      destination_attributes: {
        url: webhookUrl,
        compression: 'none'
      },
      status: 'active',
      templateArgs: {
        wallets: wallets
      }
    };

    const request = this.HttpService.post(
      `${this.baseUrl}/webhooks/template/${template}`,
      webhookData,
      { headers: this.getHeaders() }
    );
    return this.handleRequest(request, 'Create new webhook with template');
  }

  async getAllWebhooks(limit: number, offset: number){
    const request = this.HttpService.get(
    `${this.baseUrl}/webhooks?limit=${limit}&offset=${offset}`,
      { headers: this.getHeaders() }
    );
    return this.handleRequest(request, 'Get all webhooks');
  }

  async getWebhookById(id: string) {
    const request = this.HttpService.get(
      `${this.baseUrl}/webhooks/${id}`,
      { headers: this.getHeaders() }
    );
    return this.handleRequest(request, 'Get webhook by id');
  }

  async updateWebhook(id: string, dto: UpdateQnWebhookDto) {
    const request = this.HttpService.patch(
      `${this.baseUrl}/webhooks/${id}`,
      dto,
      { headers: this.getHeaders() }
    );
    return this.handleRequest(request, 'Get webhook by id' ,);
  }

  async deleteWebhook(id: string ){
    const request = this.HttpService.delete(
      `${this.baseUrl}/webhooks/${id}`,
      { headers: this.getHeaders() }
    );
    return this.handleRequest(request," Delete webhook by id")
  }

  async activateWebhook(id: string, startFrom = 'latest') {
    const request = this.HttpService.post(
      `${this.baseUrl}/webhooks/${id}/activate`,
      {startFrom},
      { headers: this.getHeaders() }
    );
    return this.handleRequest(request, 'Activate webhook by id');
  }

  async pauseWebhook(id: String) {
    const request = this.HttpService.post(
      `${this.baseUrl}/webhooks/${id}/pause`,
      {},
      { headers: this.getHeaders() }
    );
    return this.handleRequest(request, 'Pause webhook by id');
  }

  async testFilter(dto : FilterQnWebhookDto) {
    const request = this.HttpService.post(
      `${this.baseUrl}/webhooks/test_filter`,
      dto,
      { headers: this.getHeaders() }
    );
    return this.handleRequest(request, 'Test filter');
  }

  
}
