import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateNetworkDto } from './dto/create-network.dto';
import { UpdateNetworkDto } from './dto/update-network.dto';
import { InjectEntityManager } from '@nestjs/typeorm';
import { EntityManager } from 'typeorm';
import { Network } from '../networks/entities/network.entity';
import { Logger } from '@nestjs/common';

@Injectable()
export class NetworksService {
  private readonly logger = new Logger(NetworksService.name);
  
  constructor(
    @InjectEntityManager() 
    private entityManager: EntityManager, 
  ) {}

  // Get all networks
  async findAll() {
    console.log("Finding all networks");
    const networks = await this.entityManager
      .createQueryBuilder(Network, 'network') 
      .orderBy('network.name', 'ASC') 
      .getMany(); 
    
    return networks;
  }

  // Get all active networks
  async findAllActive() {
    console.log("Getting all active networks");
    const networks = await this.entityManager
      .createQueryBuilder(Network, 'network') 
      .where('network.is_active = :is_active', { is_active: true })
      .orderBy('network.name', 'ASC') 
      .getMany(); 
    
    return networks;
  }

  // Get grouped networks
  async findGrouped(): Promise<any> {
    console.log("Grouping networks");
    const networks = await this.findAllActive();
    const grouped = {};
    console.log(networks);
    
    networks.forEach(network => {
      // Skip networks without display_name
      if (!network.display_name) {
        return;
      }

      const baseName = network.display_name.split('-')[0]; // ethereum, polygon, arbitrum
      
      if (!grouped[baseName]) {
        grouped[baseName] = {
          name: baseName,
          mainnet: null,
          testnets: []
        };
      }
      
      if (network.is_testnet) {
        grouped[baseName].testnets.push(network);
      } else {
        grouped[baseName].mainnet = network;
      }
    });
    console.log(grouped);
    return Object.values(grouped);
  }

  // Find mainnets
  async findMainnets(): Promise<Network[]> {
    return this.entityManager
      .createQueryBuilder(Network, 'network')
      .where('network.is_active = :is_active', { is_active: true })
      .andWhere('network.is_testnet = :is_testnet', { is_testnet: false })
      .orderBy('network.name', 'ASC')
      .getMany();
  }

  // Find testnets
  async findTestnets(): Promise<Network[]> {
    return this.entityManager
      .createQueryBuilder(Network, 'network')
      .where('network.is_active = :is_active', { is_active: true })
      .andWhere('network.is_testnet = :is_testnet', { is_testnet: true })
      .orderBy('network.name', 'ASC')
      .getMany();
  }

  // Search via ID
  async findOne(id: number): Promise<Network> {
    const network = await this.entityManager
      .createQueryBuilder(Network, 'network')
      .where('network.id = :id', { id })
      .getOne();
    
    if (!network) {
      throw new NotFoundException(`Network with ID ${id} not found`);
    }
    
    return network;
  }

  // Create new network
  async create(createNetworkDto: CreateNetworkDto): Promise<Network> {
    const network = this.entityManager.create(Network, createNetworkDto);
    return this.entityManager.save(Network, network);
  }

  // Edit network info
  async update(id: number, updateNetworkDto: UpdateNetworkDto): Promise<Network> {
    const network = await this.findOne(id);
    Object.assign(network, updateNetworkDto);
    return this.entityManager.save(Network, network);
  }

  // Delete network
  async remove(id: number): Promise<void> {
    const result = await this.entityManager
      .createQueryBuilder()
      .delete()
      .from(Network)
      .where('id = :id', { id })
      .execute();
    
    if (result.affected === 0) {
      throw new NotFoundException(`Network with ID ${id} not found`);
    }
  }

  async pause(id: number): Promise<Network> {
    await this.findOne(id);
    
    await this.entityManager
      .createQueryBuilder()
      .update(Network)
      .set({ is_active: false })
      .where('id = :id', { id })
      .execute();
    
    return this.findOne(id);
  }

  // Activate network
  async activate(id: number): Promise<Network> {
    await this.findOne(id);

    await this.entityManager
      .createQueryBuilder()
      .update(Network)
      .set({ is_active: true })
      .where('id = :id', { id })
      .execute();

    return this.findOne(id);
  }
}