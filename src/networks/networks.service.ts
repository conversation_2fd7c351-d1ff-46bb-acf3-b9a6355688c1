import { Injectable, NotFoundException } from '@nestjs/common';
import { CreateNetworkDto } from './dto/create-network.dto';
import { UpdateNetworkDto } from './dto/update-network.dto';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { Network } from '../networks/entities/network.entity';

@Injectable()
export class NetworksService {
  constructor(
    @InjectRepository(Network)
    private networksRepository: Repository<Network>,
  ) {}

  // Get all networks
  async findAll(): Promise<Network[]> {
    return this.networksRepository.find({
      order: { name: 'ASC' },
    });
  }

  // Get all active networks
  async findAllActive(): Promise<Network[]> {
    return this.networksRepository.find({
      where: { is_active: true },
      order: { name: 'ASC' },
    });
  }

  // Get grouped networks
  async findGrouped(): Promise<any> {
    const networks = await this.findAllActive();
    const grouped = {};
    
    networks.forEach(network => {
      const baseName = network.display_name.split('-')[0]; // ethereum, polygon, arbitrum , solana
      
      if (!grouped[baseName]) {
        grouped[baseName] = {
          name: baseName,
          mainnet: null,
          testnets: []
        };
      }
      
      if (network.is_testnet) {
        grouped[baseName].testnets.push(network);
      } else {
        grouped[baseName].mainnet = network;
      }
    });
    
    return Object.values(grouped);
  }

  // Find mainnets
  async findMainnets(): Promise<Network[]> {
    return this.networksRepository.find({
      where: { is_active: true, is_testnet: false },
      order: { name: 'ASC' },
    });
  }

  // Find testnets
  async findTestnets(): Promise<Network[]> {
    return this.networksRepository.find({
      where: { is_active: true, is_testnet: true },
      order: { name: 'ASC' },
    });
  }

  // Search via ID
  async findOne(id: number): Promise<Network> {
    const network = await this.networksRepository.findOne({
      where: { id },
    });
    
    if (!network) {
      throw new NotFoundException(`Network with ID ${id} not found`);
    }
    
    return network;
  }

  // Find by chain ID (useful for webhook processing)
  async findByChainId(chainId: number): Promise<Network> {
    const network = await this.networksRepository.findOne({
      where: { chain_id: chainId, is_active: true },
    });
    
    if (!network) {
      throw new NotFoundException(`Network with chain ID ${chainId} not found`);
    }
    
    return network;
  }

  // Create new network
  async create(createNetworkDto: CreateNetworkDto): Promise<Network> {
    const network = this.networksRepository.create(createNetworkDto);
    return this.networksRepository.save(network);
  }

  // Edit network info
  async update(id: number, updateNetworkDto: UpdateNetworkDto): Promise<Network> {
    const network = await this.findOne(id);
    
    Object.assign(network, updateNetworkDto);
    
    return this.networksRepository.save(network);
  }

  // Delete network
  async remove(id: number): Promise<void> {
    const network = await this.findOne(id);
    await this.networksRepository.remove(network);
  }

  // pause network 
  // find a way to /pause on webhook 🥸
  async pause(id: number): Promise<Network> {
    const network = await this.findOne(id);
    network.is_active = false;
    return this.networksRepository.save(network);
  }

  // Activate network
  // find a way to /activate on webhook 🥵
  async activate(id: number): Promise<Network> {
    const network = await this.findOne(id);
    network.is_active = true;
    return this.networksRepository.save(network);
  }
}
