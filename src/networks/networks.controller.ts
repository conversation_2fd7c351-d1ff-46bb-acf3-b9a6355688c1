import { <PERSON>, Get, Post, Body, Patch, Param, Delete, ParseIntPipe, HttpCode, HttpStatus } from '@nestjs/common';
import { NetworksService } from './networks.service';
import { CreateNetworkDto } from './dto/create-network.dto';
import { UpdateNetworkDto } from './dto/update-network.dto';

@Controller('networks')
export class NetworksController {
  constructor(private readonly networksService: NetworksService) {}

  // Get all networks
  @Get('findAll')
  async findAll() {
    return this.networksService.findAll();
  }

  // Get all active networks
  @Get('findAllActive')
  async findAllActive() {
    return this.networksService.findAllActive();
  }

  // Get grouped networks
  @Get('grouped')
  async findGrouped() {
    return this.networksService.findGrouped();
  }

  // Find mainnets
  @Get('mainnets')
  async findMainnets() {
    return this.networksService.findMainnets();
  }

  // Find testnets
  @Get('testnets')
  async findTestnets() {
    return this.networksService.findTestnets();
  }

  // POST /networks - Create new network
  @Post()
  async create(@Body() createNetworkDto: CreateNetworkDto) {
    return this.networksService.create(createNetworkDto);
  }

  // Search via ID
  @Get(':id')
  async findOne(@Param('id', ParseIntPipe) id: number) {
    return this.networksService.findOne(id);
  }

  // Edit network info
  @Patch(':id')
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateNetworkDto: UpdateNetworkDto,
  ) {
    return this.networksService.update(id, updateNetworkDto);
  }

  //Delete network
  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(@Param('id', ParseIntPipe) id: number) {
    return this.networksService.remove(id);
  }

  //Soft delete (deactivate)
  @Patch(':id/pause')
  async pause(@Param('id', ParseIntPipe) id: number) {
    return this.networksService.pause(id);
  }

  //  Activate network
  @Patch(':id/activate')
  async activate(@Param('id', ParseIntPipe) id: number) {
    return this.networksService.activate(id);
  }
}

