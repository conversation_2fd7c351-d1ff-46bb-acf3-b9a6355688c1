import { <PERSON>, Get, Post, Body, Patch, Param, Delete } from '@nestjs/common';
import { NetworksService } from './networks.service';
import { CreateNetworkDto } from './dto/create-network.dto';
import { UpdateNetworkDto } from './dto/update-network.dto';

@Controller('networks')
export class NetworksController {
  constructor(private readonly networksService: NetworksService) {}

  // Get all networks
  @Get()
  async findAll() {
    return this.networksService.findAll();
  }

  // Get all active networks
  @Get('active')
  async findAllActive() {
    return this.networksService.findAllActive();
  }

  // Get grouped networks
  @Get('grouped')
  async findGrouped() {
    return this.networksService.findGrouped();
  }

  // GET /networks/mainnets - Find mainnets
  @Get('mainnets')
  async findMainnets() {
    return this.networksService.findMainnets();
  }

  // GET /networks/testnets - Find testnets
  @Get('testnets')
  async findTestnets() {
    return this.networksService.findTestnets();
  }

  // GET /networks/chain/:chainId - Find by chain ID
  @Get('chain/:chainId')
  async findByChainId(@Param('chainId', ParseIntPipe) chainId: number) {
    return this.networksService.findByChainId(chainId);
  }

  // POST /networks - Create new network
  @Post()
  async create(@Body() createNetworkDto: CreateNetworkDto) {
    return this.networksService.create(createNetworkDto);
  }

  // GET /networks/:id - Search via ID
  @Get(':id')
  async findOne(@Param('id', ParseIntPipe) id: number) {
    return this.networksService.findOne(id);
  }

  // PATCH /networks/:id - Edit network info
  @Patch(':id')
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateNetworkDto: UpdateNetworkDto,
  ) {
    return this.networksService.update(id, updateNetworkDto);
  }

  // DELETE /networks/:id - Delete network
  @Delete(':id')
  @HttpCode(HttpStatus.NO_CONTENT)
  async remove(@Param('id', ParseIntPipe) id: number) {
    return this.networksService.remove(id);
  }

  // PATCH /networks/:id/deactivate - Soft delete (deactivate)
  @Patch(':id/deactivate')
  async deactivate(@Param('id', ParseIntPipe) id: number) {
    return this.networksService.deactivate(id);
  }

  // PATCH /networks/:id/activate - Activate network
  @Patch(':id/activate')
  async activate(@Param('id', ParseIntPipe) id: number) {
    return this.networksService.activate(id);
  }
}

