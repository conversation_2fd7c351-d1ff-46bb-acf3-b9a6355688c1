import { Column, CreateDate<PERSON><PERSON>umn, <PERSON><PERSON><PERSON>, PrimaryGeneratedColumn, UpdateDateColumn } from "typeorm";

@Entity({ name: 'networks' })
export class Network {
  @PrimaryGeneratedColumn()
  id: number;

  @Column({ nullable: true })
  display_name: string; // ethereum-mainnet

  @Column({ nullable: true })
  name: string; // Ethereum Mainnet

  @Column({ nullable: true })
  symbol: string; // ETH

  @Column({ unique: true, nullable: true })
  chain_id: number; // 1

  @Column({ nullable: true })
  block_time_seconds: number; // 1-12

  @Column({ nullable: true })
  rpc_url: string; // https://weathered-young-aura.ethereum-sepolia.quiknode.pro/.../

  @Column({ nullable: true })
  quicknode_subdomain: string;

  @Column({ default: true })
  is_active: boolean;

  @Column({ default: false })
  is_testnet: boolean;

  @CreateDateColumn()
  created_at: Date;
}