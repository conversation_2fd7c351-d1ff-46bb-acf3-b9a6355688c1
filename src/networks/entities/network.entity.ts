import { Column, CreateDateColumn, <PERSON><PERSON>ty, PrimaryGeneratedColumn, UpdateDateColumn } from "typeorm";

@Entity({ name: 'networks' })
export class Network {
  @PrimaryGeneratedColumn()
  id: number;

  @Column()
  display_name: string; // ethereum-mainnet

  @Column()
  name: string; // Ethereum Mainnet

  @Column()
  symbol: string; // ETH

  @Column({ unique: true })
  chain_id: number; // 1

  @Column()
  block_time_seconds: number; // 1-12

  @Column()
  rpc_url: string; // https://weathered-young-aura.ethereum-sepolia.quiknode.pro/.../

  @Column({ nullable: true})
  quicknode_subdomain: string;

  @Column({ default: true })
  is_active: boolean;

  @Column({ default: false })
  is_testnet: boolean;

  @CreateDateColumn()
  created_at: Date;
}