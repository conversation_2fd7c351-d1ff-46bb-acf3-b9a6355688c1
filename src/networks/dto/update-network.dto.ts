import { ApiProperty, PartialType } from '@nestjs/swagger';
import { CreateNetworkDto } from './create-network.dto';
import { IsString, IsNumber, IsBoolean, IsOptional, IsUrl, IsNotEmpty } from 'class-validator';

export class UpdateNetworkDto extends PartialType(CreateNetworkDto) {
  @ApiProperty({ example: 'ethereum-mainnet'})
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  display_name?: string;

  @ApiProperty({ example: 'Ethereum Mainnet'})
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  name?: string;

  @ApiProperty({ example: 'ETH'})
  @IsOptional()
  @IsString()
  @IsNotEmpty()
  symbol?: string;

  @ApiProperty({ example: 1 })
  @IsOptional()
  @IsNumber()
  chain_id?: number;

  @ApiProperty({ example: 12 })
  @IsOptional()
  @IsNumber()
  block_time_seconds?: number;

  @ApiProperty({ example: 'https://weathered-young-aura.ethereum-sepolia.quiknode.pro/1ecc5b9d230d6b74ec0b4e0104fab1a6759b1ac5/'})
  @IsOptional()
  @IsUrl()
  rpc_url?: string;

  @ApiProperty({ example: 'ethereum-sepolia'})
  @IsOptional()
  @IsString()
  quicknode_subdomain?: string;

  @ApiProperty({ example: true })
  @IsOptional()
  @IsBoolean()
  is_active?: boolean;

  @IsOptional()
  @IsBoolean()
  is_testnet?: boolean;
}
