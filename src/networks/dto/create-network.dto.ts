import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNumber, IsBoolean, IsOptional, IsUrl } from 'class-validator';

export class CreateNetworkDto {
  @ApiProperty({ example: 'ethereum-mainnet'})
  @IsString()
  display_name: string;

  @ApiProperty({ example: 'Ethereum Mainnet'})
  @IsString()
  name: string;

  @ApiProperty({ example: 'ETH'})
  @IsString()
  symbol: string;

  @ApiProperty({ example: 1 })
  @IsNumber()
  chain_id: number;

  @ApiProperty({ example: 12 })
  @IsNumber()
  block_time_seconds: number;

  @ApiProperty({ example: 'https://weathered-young-aura.ethereum-sepolia.quiknode.pro/1ecc5b9d230d6b74ec0b4e0104fab1a6759b1ac5/'})
  @IsUrl()
  rpc_url: string;

  @ApiProperty({ example: 'ethereum-sepolia'})
  @IsOptional()
  @IsString()
  quicknode_subdomain?: string;

  @ApiProperty({ example: true })
  @IsOptional()
  @IsBoolean()
  is_active?: boolean;

  @IsOptional()
  @IsBoolean()
  is_testnet?: boolean;
}