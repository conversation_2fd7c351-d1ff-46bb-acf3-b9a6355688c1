import { IsString, IsN<PERSON>ber, IsBoolean, IsOptional, IsUrl } from 'class-validator';

export class CreateNetworkDto {
  @IsString()
  display_name: string;

  @IsString()
  name: string;

  @IsString()
  symbol: string;

  @IsNumber()
  chain_id: number;

  @IsNumber()
  block_time_seconds: number;

  @IsUrl()
  rpc_url: string;

  @IsOptional()
  @IsString()
  quicknode_subdomain?: string;

  @IsOptional()
  @IsBoolean()
  is_active?: boolean;

  @IsOptional()
  @IsBoolean()
  is_testnet?: boolean;
}